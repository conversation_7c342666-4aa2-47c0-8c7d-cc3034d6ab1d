import{r as c,R as re}from"./vendor-Gm9i_4Ku.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function S(){return S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},S.apply(this,arguments)}var P;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(P||(P={}));const _="popstate";function ae(e){e===void 0&&(e={});function t(r,a){let{pathname:l,search:i,hash:s}=r.location;return $("",{pathname:l,search:i,hash:s},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:q(a)}return ie(t,n,null,e)}function v(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function J(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function le(){return Math.random().toString(36).substr(2,8)}function j(e,t){return{usr:e.state,key:e.key,idx:t}}function $(e,t,n,r){return n===void 0&&(n=null),S({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?b(t):t,{state:n,key:t&&t.key||r||le()})}function q(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function b(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function ie(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,s=P.Pop,o=null,f=h();f==null&&(f=0,i.replaceState(S({},i.state,{idx:f}),""));function h(){return(i.state||{idx:null}).idx}function u(){s=P.Pop;let d=h(),x=d==null?null:d-f;f=d,o&&o({action:s,location:m.location,delta:x})}function p(d,x){s=P.Push;let E=$(m.location,d,x);f=h()+1;let C=j(E,f),I=m.createHref(E);try{i.pushState(C,"",I)}catch(O){if(O instanceof DOMException&&O.name==="DataCloneError")throw O;a.location.assign(I)}l&&o&&o({action:s,location:m.location,delta:1})}function y(d,x){s=P.Replace;let E=$(m.location,d,x);f=h();let C=j(E,f),I=m.createHref(E);i.replaceState(C,"",I),l&&o&&o({action:s,location:m.location,delta:0})}function g(d){let x=a.location.origin!=="null"?a.location.origin:a.location.href,E=typeof d=="string"?d:q(d);return E=E.replace(/ $/,"%20"),v(x,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,x)}let m={get action(){return s},get location(){return e(a,i)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(_,u),o=d,()=>{a.removeEventListener(_,u),o=null}},createHref(d){return t(a,d)},createURL:g,encodeLocation(d){let x=g(d);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:y,go(d){return i.go(d)}};return m}var M;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(M||(M={}));function oe(e,t,n){return n===void 0&&(n="/"),se(e,t,n)}function se(e,t,n,r){let a=typeof t=="string"?b(t):t,l=Q(a.pathname||"/",n);if(l==null)return null;let i=G(e);ue(i);let s=null;for(let o=0;s==null&&o<i.length;++o){let f=Ce(l);s=ye(i[o],f)}return s}function G(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(l,i,s)=>{let o={relativePath:s===void 0?l.path||"":s,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};o.relativePath.startsWith("/")&&(v(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let f=R([r,o.relativePath]),h=n.concat(o);l.children&&l.children.length>0&&(v(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),G(l.children,t,h,f)),!(l.path==null&&!l.index)&&t.push({path:f,score:ve(f,l.index),routesMeta:h})};return e.forEach((l,i)=>{var s;if(l.path===""||!((s=l.path)!=null&&s.includes("?")))a(l,i);else for(let o of K(l.path))a(l,i,o)}),t}function K(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return a?[l,""]:[l];let i=K(r.join("/")),s=[];return s.push(...i.map(o=>o===""?l:[l,o].join("/"))),a&&s.push(...i),s.map(o=>e.startsWith("/")&&o===""?"/":o)}function ue(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:ge(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const ce=/^:[\w-]+$/,he=3,fe=2,de=1,pe=10,me=-2,k=e=>e==="*";function ve(e,t){let n=e.split("/"),r=n.length;return n.some(k)&&(r+=me),t&&(r+=fe),n.filter(a=>!k(a)).reduce((a,l)=>a+(ce.test(l)?he:l===""?de:pe),r)}function ge(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function ye(e,t,n){let{routesMeta:r}=e,a={},l="/",i=[];for(let s=0;s<r.length;++s){let o=r[s],f=s===r.length-1,h=l==="/"?t:t.slice(l.length)||"/",u=xe({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},h),p=o.route;if(!u)return null;Object.assign(a,u.params),i.push({params:a,pathname:R([l,u.pathname]),pathnameBase:Be(R([l,u.pathnameBase])),route:p}),u.pathnameBase!=="/"&&(l=R([l,u.pathnameBase]))}return i}function xe(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Ee(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce((f,h,u)=>{let{paramName:p,isOptional:y}=h;if(p==="*"){let m=s[u]||"";i=l.slice(0,l.length-m.length).replace(/(.)\/+$/,"$1")}const g=s[u];return y&&!g?f[p]=void 0:f[p]=(g||"").replace(/%2F/g,"/"),f},{}),pathname:l,pathnameBase:i,pattern:e}}function Ee(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),J(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,s,o)=>(r.push({paramName:s,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function Ce(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return J(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Q(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Pe(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?b(e):e;return{pathname:n?n.startsWith("/")?n:Re(n,t):t,search:Ie(r),hash:Ue(a)}}function Re(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function T(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function we(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function be(e,t){let n=we(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Se(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=b(e):(a=S({},e),v(!a.pathname||!a.pathname.includes("?"),T("?","pathname","search",a)),v(!a.pathname||!a.pathname.includes("#"),T("#","pathname","hash",a)),v(!a.search||!a.search.includes("#"),T("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,s;if(i==null)s=n;else{let u=t.length-1;if(!r&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),u-=1;a.pathname=p.join("/")}s=u>=0?t[u]:"/"}let o=Pe(a,s),f=i&&i!=="/"&&i.endsWith("/"),h=(l||i===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(f||h)&&(o.pathname+="/"),o}const R=e=>e.join("/").replace(/\/\/+/g,"/"),Be=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ie=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ue=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Le(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const X=["post","put","patch","delete"];new Set(X);const Ne=["get",...X];new Set(Ne);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)}const W=c.createContext(null),Oe=c.createContext(null),U=c.createContext(null),L=c.createContext(null),w=c.createContext({outlet:null,matches:[],isDataRoute:!1}),Y=c.createContext(null);function N(){return c.useContext(L)!=null}function Z(){return N()||v(!1),c.useContext(L).location}function H(e){c.useContext(U).static||c.useLayoutEffect(e)}function He(){let{isDataRoute:e}=c.useContext(w);return e?Je():Te()}function Te(){N()||v(!1);let e=c.useContext(W),{basename:t,future:n,navigator:r}=c.useContext(U),{matches:a}=c.useContext(w),{pathname:l}=Z(),i=JSON.stringify(be(a,n.v7_relativeSplatPath)),s=c.useRef(!1);return H(()=>{s.current=!0}),c.useCallback(function(f,h){if(h===void 0&&(h={}),!s.current)return;if(typeof f=="number"){r.go(f);return}let u=Se(f,JSON.parse(i),l,h.relative==="path");e==null&&t!=="/"&&(u.pathname=u.pathname==="/"?t:R([t,u.pathname])),(h.replace?r.replace:r.push)(u,h.state,h)},[t,r,i,l,e])}function et(){let{matches:e}=c.useContext(w),t=e[e.length-1];return t?t.params:{}}function $e(e,t){return Fe(e,t)}function Fe(e,t,n,r){N()||v(!1);let{navigator:a}=c.useContext(U),{matches:l}=c.useContext(w),i=l[l.length-1],s=i?i.params:{};i&&i.pathname;let o=i?i.pathnameBase:"/";i&&i.route;let f=Z(),h;if(t){var u;let d=typeof t=="string"?b(t):t;o==="/"||(u=d.pathname)!=null&&u.startsWith(o)||v(!1),h=d}else h=f;let p=h.pathname||"/",y=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let g=oe(e,{pathname:y}),m=ke(g&&g.map(d=>Object.assign({},d,{params:Object.assign({},s,d.params),pathname:R([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:R([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),l,n,r);return t&&m?c.createElement(L.Provider,{value:{location:B({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:P.Pop}},m):m}function We(){let e=Ae(),t=Le(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return c.createElement(c.Fragment,null,c.createElement("h2",null,"Unexpected Application Error!"),c.createElement("h3",{style:{fontStyle:"italic"}},t),n?c.createElement("pre",{style:a},n):null,null)}const _e=c.createElement(We,null);class je extends c.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?c.createElement(w.Provider,{value:this.props.routeContext},c.createElement(Y.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Me(e){let{routeContext:t,match:n,children:r}=e,a=c.useContext(W);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),c.createElement(w.Provider,{value:t},r)}function ke(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var l;if(!n)return null;if(n.errors)e=n.matches;else if((l=r)!=null&&l.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,s=(a=n)==null?void 0:a.errors;if(s!=null){let h=i.findIndex(u=>u.route.id&&(s==null?void 0:s[u.route.id])!==void 0);h>=0||v(!1),i=i.slice(0,Math.min(i.length,h+1))}let o=!1,f=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<i.length;h++){let u=i[h];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(f=h),u.route.id){let{loaderData:p,errors:y}=n,g=u.route.loader&&p[u.route.id]===void 0&&(!y||y[u.route.id]===void 0);if(u.route.lazy||g){o=!0,f>=0?i=i.slice(0,f+1):i=[i[0]];break}}}return i.reduceRight((h,u,p)=>{let y,g=!1,m=null,d=null;n&&(y=s&&u.route.id?s[u.route.id]:void 0,m=u.route.errorElement||_e,o&&(f<0&&p===0?(qe("route-fallback"),g=!0,d=null):f===p&&(g=!0,d=u.route.hydrateFallbackElement||null)));let x=t.concat(i.slice(0,p+1)),E=()=>{let C;return y?C=m:g?C=d:u.route.Component?C=c.createElement(u.route.Component,null):u.route.element?C=u.route.element:C=h,c.createElement(Me,{match:u,routeContext:{outlet:h,matches:x,isDataRoute:n!=null},children:C})};return n&&(u.route.ErrorBoundary||u.route.errorElement||p===0)?c.createElement(je,{location:n.location,revalidation:n.revalidation,component:m,error:y,children:E(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):E()},null)}var ee=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ee||{}),te=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(te||{});function Ve(e){let t=c.useContext(W);return t||v(!1),t}function ze(e){let t=c.useContext(Oe);return t||v(!1),t}function De(e){let t=c.useContext(w);return t||v(!1),t}function ne(e){let t=De(),n=t.matches[t.matches.length-1];return n.route.id||v(!1),n.route.id}function Ae(){var e;let t=c.useContext(Y),n=ze(),r=ne();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Je(){let{router:e}=Ve(ee.UseNavigateStable),t=ne(te.UseNavigateStable),n=c.useRef(!1);return H(()=>{n.current=!0}),c.useCallback(function(a,l){l===void 0&&(l={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,B({fromRouteId:t},l)))},[e,t])}const V={};function qe(e,t,n){V[e]||(V[e]=!0)}function Ge(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function Ke(e){v(!1)}function Qe(e){let{basename:t="/",children:n=null,location:r,navigationType:a=P.Pop,navigator:l,static:i=!1,future:s}=e;N()&&v(!1);let o=t.replace(/^\/*/,"/"),f=c.useMemo(()=>({basename:o,navigator:l,static:i,future:B({v7_relativeSplatPath:!1},s)}),[o,s,l,i]);typeof r=="string"&&(r=b(r));let{pathname:h="/",search:u="",hash:p="",state:y=null,key:g="default"}=r,m=c.useMemo(()=>{let d=Q(h,o);return d==null?null:{location:{pathname:d,search:u,hash:p,state:y,key:g},navigationType:a}},[o,h,u,p,y,g,a]);return m==null?null:c.createElement(U.Provider,{value:f},c.createElement(L.Provider,{children:n,value:m}))}function tt(e){let{children:t,location:n}=e;return $e(F(t),n)}new Promise(()=>{});function F(e,t){t===void 0&&(t=[]);let n=[];return c.Children.forEach(e,(r,a)=>{if(!c.isValidElement(r))return;let l=[...t,a];if(r.type===c.Fragment){n.push.apply(n,F(r.props.children,l));return}r.type!==Ke&&v(!1),!r.props.index||!r.props.children||v(!1);let i={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=F(r.props.children,l)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Xe="6";try{window.__reactRouterVersion=Xe}catch{}const Ye="startTransition",z=re[Ye];function nt(e){let{basename:t,children:n,future:r,window:a}=e,l=c.useRef();l.current==null&&(l.current=ae({window:a,v5Compat:!0}));let i=l.current,[s,o]=c.useState({action:i.action,location:i.location}),{v7_startTransition:f}=r||{},h=c.useCallback(u=>{f&&z?z(()=>o(u)):o(u)},[o,f]);return c.useLayoutEffect(()=>i.listen(h),[i,h]),c.useEffect(()=>Ge(r),[r]),c.createElement(Qe,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:i,future:r})}var D;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(D||(D={}));var A;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(A||(A={}));export{nt as B,tt as R,et as a,Ke as b,He as u};
