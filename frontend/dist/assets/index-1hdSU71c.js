import{r as v,a as bt,b as jt}from"./vendor-Gm9i_4Ku.js";import{u as je,a as ze,B as Nt,R as wt,b as ce}from"./router-CyajUmd-.js";import{M as vt,T as z,A as _e,a as ee,X as He,F as Ne,U as Je,C as Q,V as T,b as we,Z as St,c as Rt,d as Et,e as q,f as M,G as At,E as _t,D as Tt,g as Ct,h as Ot,i as Pt,j as kt,k as Dt,S as Lt,l as Ft,m as Ut,n as Bt,o as It,O as qt}from"./ui-CYuAx2AN.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const i of a)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(a){const i={};return a.integrity&&(i.integrity=a.integrity),a.referrerPolicy&&(i.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?i.credentials="include":a.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(a){if(a.ep)return;a.ep=!0;const i=n(a);fetch(a.href,i)}})();var Ve={exports:{}},te={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mt=v,$t=Symbol.for("react.element"),zt=Symbol.for("react.fragment"),Ht=Object.prototype.hasOwnProperty,Jt=Mt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Vt={key:!0,ref:!0,__self:!0,__source:!0};function We(e,t,n){var r,a={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Ht.call(t,r)&&!Vt.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)a[r]===void 0&&(a[r]=t[r]);return{$$typeof:$t,type:e,key:i,ref:o,props:a,_owner:Jt.current}}te.Fragment=zt;te.jsx=We;te.jsxs=We;Ve.exports=te;var s=Ve.exports,fe={},Te=bt;fe.createRoot=Te.createRoot,fe.hydrateRoot=Te.hydrateRoot;const Wt=({systemStatus:e,onMenuClick:t})=>{const n=()=>{switch(e){case"healthy":return s.jsx(_e,{className:"h-4 w-4 text-green-500"});case"loading":return s.jsx("div",{className:"loading-spinner"});case"error":return s.jsx(ee,{className:"h-4 w-4 text-red-500"});default:return s.jsx(_e,{className:"h-4 w-4 text-gray-400"})}},r=()=>{switch(e){case"healthy":return"System Healthy";case"loading":return"Connecting...";case"error":return"System Error";default:return"Unknown"}};return s.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"flex justify-between items-center h-16",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("button",{onClick:t,className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500",children:s.jsx(vt,{className:"h-6 w-6"})}),s.jsx("div",{className:"flex items-center space-x-3",children:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:s.jsx("span",{className:"text-white font-bold text-sm",children:"R"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"Rozie AI"}),s.jsx("p",{className:"text-xs text-gray-500",children:"Liability Intelligence Platform"})]})]})})]}),s.jsx("div",{className:"hidden md:flex items-center space-x-4",children:s.jsxs("div",{className:"flex items-center space-x-2 px-4 py-2 bg-blue-50 rounded-lg",children:[s.jsx(z,{className:"h-5 w-5 text-blue-600"}),s.jsx("span",{className:"text-blue-900 font-medium",children:"Liability Decisions"})]})}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[n(),s.jsx("span",{className:"text-sm text-gray-600 hidden sm:inline",children:r()})]}),s.jsx("div",{className:"text-sm text-gray-500",children:s.jsx("span",{className:"font-medium",children:"v1.0.0"})})]})]})})})},Kt=({isOpen:e,onClose:t})=>e?s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:t}),s.jsxs("div",{className:"fixed left-0 top-0 h-full w-64 bg-white shadow-lg z-50 transform transition-transform duration-300 ease-in-out",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Navigation"}),s.jsx("button",{onClick:t,className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",children:s.jsx(He,{className:"h-5 w-5"})})]}),s.jsxs("div",{className:"p-4",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-500 uppercase tracking-wide mb-3",children:"Current Solution"}),s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(z,{className:"h-5 w-5 text-blue-600"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-blue-900",children:"Liability Decisions"}),s.jsx("div",{className:"text-sm text-blue-700 mt-1",children:"Intelligent fault assessment and liability analysis"})]})]})})]}),s.jsxs("div",{className:"p-4 border-t border-gray-200",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-500 uppercase tracking-wide mb-3",children:"Menu"}),s.jsx("div",{className:"space-y-1",children:s.jsxs("button",{className:"w-full flex items-center p-2 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:[s.jsx(Ne,{className:"h-5 w-5 mr-3 text-gray-400"}),s.jsx("span",{children:"Documents"})]})})]}),s.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-gray-50",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-sm font-medium text-gray-900",children:"Rozie AI"}),s.jsx("div",{className:"text-xs text-gray-500",children:"Liability Intelligence Platform"})]})})]})]}):null,Gt=({onUpload:e,isUploading:t})=>{const[n,r]=v.useState(!1),[a,i]=v.useState([]),o=v.useRef(null),d=u=>{u.preventDefault(),u.stopPropagation(),u.type==="dragenter"||u.type==="dragover"?r(!0):u.type==="dragleave"&&r(!1)},h=u=>{u.preventDefault(),u.stopPropagation(),r(!1),u.dataTransfer.files&&u.dataTransfer.files[0]&&c(u.dataTransfer.files)},m=u=>{u.preventDefault(),u.target.files&&u.target.files[0]&&c(u.target.files)},c=u=>{const b=Array.from(u).map(N=>({file:N,id:Math.random().toString(36).substr(2,9),name:N.name,size:N.size,type:N.type,status:"ready"}));i(N=>[...N,...b])},f=u=>{i(b=>b.filter(N=>N.id!==u))},g=u=>{if(u===0)return"0 Bytes";const b=1024,N=["Bytes","KB","MB","GB"],w=Math.floor(Math.log(u)/Math.log(b));return parseFloat((u/Math.pow(b,w)).toFixed(2))+" "+N[w]},j=u=>u.includes("image")?"🖼️":u.includes("pdf")?"📄":u.includes("word")?"📝":u.includes("excel")||u.includes("spreadsheet")?"📊":u.includes("text")||u.includes("plain")?"📝":"📄",x=()=>{a.length!==0&&e(a.map(u=>u.file))};return s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:`file-upload-zone ${n?"drag-active":""}`,onDragEnter:d,onDragLeave:d,onDragOver:d,onDrop:h,onClick:()=>{var u;return(u=o.current)==null?void 0:u.click()},children:[s.jsx("input",{ref:o,type:"file",multiple:!0,onChange:m,accept:".pdf,.jpg,.jpeg,.png,.doc,.docx,.txt,.msg,.tiff,.tif,text/plain,application/pdf,image/*",className:"hidden"}),s.jsxs("div",{className:"text-center",children:[s.jsx(Je,{className:`mx-auto h-12 w-12 ${n?"text-blue-500":"text-gray-400"}`}),s.jsxs("div",{className:"mt-4",children:[s.jsx("p",{className:"text-lg font-medium text-gray-900",children:n?"Drop files here":"Upload Documents"}),s.jsx("p",{className:"text-gray-500 mt-1",children:"Drag and drop files here, or click to select"}),s.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Supported: PDF, Images, Word documents, Text files, TIFF files"})]})]})]}),a.length>0&&s.jsxs("div",{className:"space-y-2",children:[s.jsxs("h4",{className:"font-medium text-gray-900",children:["Selected Files (",a.length,")"]}),s.jsx("div",{className:"max-h-48 overflow-y-auto space-y-2",children:a.map(u=>s.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg border",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("span",{className:"text-2xl",children:j(u.type)}),s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-gray-900 truncate max-w-xs",children:u.name}),s.jsx("div",{className:"text-sm text-gray-500",children:g(u.size)})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[u.status==="ready"&&s.jsx(Q,{className:"h-5 w-5 text-green-500"}),u.status==="error"&&s.jsx(ee,{className:"h-5 w-5 text-red-500"}),s.jsx("button",{onClick:()=>f(u.id),className:"p-1 text-gray-400 hover:text-red-500 transition-colors",disabled:t,children:s.jsx(He,{className:"h-4 w-4"})})]})]},u.id))})]}),a.length>0&&s.jsxs("div",{className:"flex justify-between items-center pt-4 border-t",children:[s.jsxs("div",{className:"text-sm text-gray-600",children:[a.length," file",a.length!==1?"s":""," ready for processing"]}),s.jsxs("div",{className:"space-x-2",children:[s.jsx("button",{onClick:()=>i([]),disabled:t,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Clear All"}),s.jsx("button",{onClick:x,disabled:t||a.length===0,className:`px-6 py-2 rounded-lg font-medium transition-colors ${t?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:t?s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"loading-spinner"}),s.jsx("span",{children:"Uploading..."})]}):`Process ${a.length} File${a.length!==1?"s":""}`})]})]}),s.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[s.jsx("h5",{className:"font-medium text-blue-900 mb-2",children:"Liability Decision - Document Requirements"}),s.jsx("div",{className:"text-sm text-blue-800",children:s.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[s.jsx("li",{children:"Incident reports and witness statements"}),s.jsx("li",{children:"Photos and video evidence"}),s.jsx("li",{children:"Legal documents and correspondence"}),s.jsx("li",{children:"Medical reports (if applicable)"}),s.jsx("li",{children:"Police reports and official documentation"})]})})]})]})},Xt=()=>{const e=je(),[t,n]=v.useState(!1),r=async i=>{if(!i||i.length===0){T.error("Please select files to upload");return}n(!0);try{T.loading("Uploading files...",{id:"upload"}),await new Promise(d=>setTimeout(d,2e3));const o=`job_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;T.success("Files uploaded successfully! Starting processing...",{id:"upload"}),e(`/processing/${o}`)}catch(o){console.error("Upload failed:",o),T.error("Upload failed. Please try again.",{id:"upload"})}finally{n(!1)}},a={name:"Liability Decisions",description:"Intelligent fault assessment and liability analysis"};return s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg text-white p-8",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[s.jsx("div",{className:"w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center",children:s.jsx("span",{className:"text-white font-bold text-lg",children:"R"})}),s.jsx("h1",{className:"text-3xl font-bold",children:"Rozie AI"})]}),s.jsx("p",{className:"text-blue-100 text-lg mb-4",children:"Liability Intelligence Platform"}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(z,{className:"h-5 w-5"}),s.jsx("span",{className:"font-medium",children:a.name})]}),s.jsx("div",{className:"text-blue-200",children:"•"}),s.jsx("div",{className:"text-blue-200",children:a.description})]})]}),s.jsx("div",{className:"hidden md:block",children:s.jsxs("div",{className:"text-right",children:[s.jsx("div",{className:"text-2xl font-bold",children:"95%"}),s.jsx("div",{className:"text-blue-200",children:"Accuracy Rate"})]})})]})}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[s.jsx("div",{className:"lg:col-span-2 space-y-6",children:s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[s.jsx(Je,{className:"h-6 w-6 text-blue-600"}),s.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Upload Documents"})]}),s.jsx(Gt,{onUpload:r,isUploading:t})]})}),s.jsx("div",{className:"space-y-6",children:s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Solution Overview"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(z,{className:"h-5 w-5 text-blue-600"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-gray-900",children:"Liability Decisions"}),s.jsx("div",{className:"text-sm text-gray-600",children:"AI-powered fault assessment"})]})]}),s.jsx("div",{className:"pt-4 border-t border-gray-200",children:s.jsxs("div",{className:"text-sm text-gray-600 space-y-2",children:[s.jsx("div",{children:"• Advanced document processing"}),s.jsx("div",{children:"• Evidence extraction & analysis"}),s.jsx("div",{children:"• Fault percentage calculation"}),s.jsx("div",{children:"• Confidence scoring"}),s.jsx("div",{children:"• Canadian legal compliance"})]})})]})]})})]})]})};function Ke(e,t){return function(){return e.apply(t,arguments)}}const{toString:Qt}=Object.prototype,{getPrototypeOf:ve}=Object,{iterator:se,toStringTag:Ge}=Symbol,ne=(e=>t=>{const n=Qt.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),C=e=>(e=e.toLowerCase(),t=>ne(t)===e),re=e=>t=>typeof t===e,{isArray:B}=Array,H=re("undefined");function Zt(e){return e!==null&&!H(e)&&e.constructor!==null&&!H(e.constructor)&&A(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Xe=C("ArrayBuffer");function Yt(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Xe(e.buffer),t}const es=re("string"),A=re("function"),Qe=re("number"),ae=e=>e!==null&&typeof e=="object",ts=e=>e===!0||e===!1,K=e=>{if(ne(e)!=="object")return!1;const t=ve(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ge in e)&&!(se in e)},ss=C("Date"),ns=C("File"),rs=C("Blob"),as=C("FileList"),is=e=>ae(e)&&A(e.pipe),os=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||A(e.append)&&((t=ne(e))==="formdata"||t==="object"&&A(e.toString)&&e.toString()==="[object FormData]"))},ls=C("URLSearchParams"),[cs,ds,us,ms]=["ReadableStream","Request","Response","Headers"].map(C),fs=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function J(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,a;if(typeof e!="object"&&(e=[e]),B(e))for(r=0,a=e.length;r<a;r++)t.call(null,e[r],r,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let d;for(r=0;r<o;r++)d=i[r],t.call(null,e[d],d,e)}}function Ze(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,a;for(;r-- >0;)if(a=n[r],t===a.toLowerCase())return a;return null}const L=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Ye=e=>!H(e)&&e!==L;function he(){const{caseless:e}=Ye(this)&&this||{},t={},n=(r,a)=>{const i=e&&Ze(t,a)||a;K(t[i])&&K(r)?t[i]=he(t[i],r):K(r)?t[i]=he({},r):B(r)?t[i]=r.slice():t[i]=r};for(let r=0,a=arguments.length;r<a;r++)arguments[r]&&J(arguments[r],n);return t}const hs=(e,t,n,{allOwnKeys:r}={})=>(J(t,(a,i)=>{n&&A(a)?e[i]=Ke(a,n):e[i]=a},{allOwnKeys:r}),e),xs=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),ps=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},gs=(e,t,n,r)=>{let a,i,o;const d={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),i=a.length;i-- >0;)o=a[i],(!r||r(o,e,t))&&!d[o]&&(t[o]=e[o],d[o]=!0);e=n!==!1&&ve(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},ys=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},bs=e=>{if(!e)return null;if(B(e))return e;let t=e.length;if(!Qe(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},js=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ve(Uint8Array)),Ns=(e,t)=>{const r=(e&&e[se]).call(e);let a;for(;(a=r.next())&&!a.done;){const i=a.value;t.call(e,i[0],i[1])}},ws=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},vs=C("HTMLFormElement"),Ss=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,a){return r.toUpperCase()+a}),Ce=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Rs=C("RegExp"),et=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};J(n,(a,i)=>{let o;(o=t(a,i,e))!==!1&&(r[i]=o||a)}),Object.defineProperties(e,r)},Es=e=>{et(e,(t,n)=>{if(A(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(A(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},As=(e,t)=>{const n={},r=a=>{a.forEach(i=>{n[i]=!0})};return B(e)?r(e):r(String(e).split(t)),n},_s=()=>{},Ts=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Cs(e){return!!(e&&A(e.append)&&e[Ge]==="FormData"&&e[se])}const Os=e=>{const t=new Array(10),n=(r,a)=>{if(ae(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[a]=r;const i=B(r)?[]:{};return J(r,(o,d)=>{const h=n(o,a+1);!H(h)&&(i[d]=h)}),t[a]=void 0,i}}return r};return n(e,0)},Ps=C("AsyncFunction"),ks=e=>e&&(ae(e)||A(e))&&A(e.then)&&A(e.catch),tt=((e,t)=>e?setImmediate:t?((n,r)=>(L.addEventListener("message",({source:a,data:i})=>{a===L&&i===n&&r.length&&r.shift()()},!1),a=>{r.push(a),L.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",A(L.postMessage)),Ds=typeof queueMicrotask<"u"?queueMicrotask.bind(L):typeof process<"u"&&process.nextTick||tt,Ls=e=>e!=null&&A(e[se]),l={isArray:B,isArrayBuffer:Xe,isBuffer:Zt,isFormData:os,isArrayBufferView:Yt,isString:es,isNumber:Qe,isBoolean:ts,isObject:ae,isPlainObject:K,isReadableStream:cs,isRequest:ds,isResponse:us,isHeaders:ms,isUndefined:H,isDate:ss,isFile:ns,isBlob:rs,isRegExp:Rs,isFunction:A,isStream:is,isURLSearchParams:ls,isTypedArray:js,isFileList:as,forEach:J,merge:he,extend:hs,trim:fs,stripBOM:xs,inherits:ps,toFlatObject:gs,kindOf:ne,kindOfTest:C,endsWith:ys,toArray:bs,forEachEntry:Ns,matchAll:ws,isHTMLForm:vs,hasOwnProperty:Ce,hasOwnProp:Ce,reduceDescriptors:et,freezeMethods:Es,toObjectSet:As,toCamelCase:Ss,noop:_s,toFiniteNumber:Ts,findKey:Ze,global:L,isContextDefined:Ye,isSpecCompliantForm:Cs,toJSONObject:Os,isAsyncFn:Ps,isThenable:ks,setImmediate:tt,asap:Ds,isIterable:Ls};function y(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}l.inherits(y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:l.toJSONObject(this.config),code:this.code,status:this.status}}});const st=y.prototype,nt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{nt[e]={value:e}});Object.defineProperties(y,nt);Object.defineProperty(st,"isAxiosError",{value:!0});y.from=(e,t,n,r,a,i)=>{const o=Object.create(st);return l.toFlatObject(e,o,function(h){return h!==Error.prototype},d=>d!=="isAxiosError"),y.call(o,e.message,t,n,r,a),o.cause=e,o.name=e.name,i&&Object.assign(o,i),o};const Fs=null;function xe(e){return l.isPlainObject(e)||l.isArray(e)}function rt(e){return l.endsWith(e,"[]")?e.slice(0,-2):e}function Oe(e,t,n){return e?e.concat(t).map(function(a,i){return a=rt(a),!n&&i?"["+a+"]":a}).join(n?".":""):t}function Us(e){return l.isArray(e)&&!e.some(xe)}const Bs=l.toFlatObject(l,{},null,function(t){return/^is[A-Z]/.test(t)});function ie(e,t,n){if(!l.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=l.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(p,u){return!l.isUndefined(u[p])});const r=n.metaTokens,a=n.visitor||c,i=n.dots,o=n.indexes,h=(n.Blob||typeof Blob<"u"&&Blob)&&l.isSpecCompliantForm(t);if(!l.isFunction(a))throw new TypeError("visitor must be a function");function m(x){if(x===null)return"";if(l.isDate(x))return x.toISOString();if(l.isBoolean(x))return x.toString();if(!h&&l.isBlob(x))throw new y("Blob is not supported. Use a Buffer instead.");return l.isArrayBuffer(x)||l.isTypedArray(x)?h&&typeof Blob=="function"?new Blob([x]):Buffer.from(x):x}function c(x,p,u){let b=x;if(x&&!u&&typeof x=="object"){if(l.endsWith(p,"{}"))p=r?p:p.slice(0,-2),x=JSON.stringify(x);else if(l.isArray(x)&&Us(x)||(l.isFileList(x)||l.endsWith(p,"[]"))&&(b=l.toArray(x)))return p=rt(p),b.forEach(function(w,P){!(l.isUndefined(w)||w===null)&&t.append(o===!0?Oe([p],P,i):o===null?p:p+"[]",m(w))}),!1}return xe(x)?!0:(t.append(Oe(u,p,i),m(x)),!1)}const f=[],g=Object.assign(Bs,{defaultVisitor:c,convertValue:m,isVisitable:xe});function j(x,p){if(!l.isUndefined(x)){if(f.indexOf(x)!==-1)throw Error("Circular reference detected in "+p.join("."));f.push(x),l.forEach(x,function(b,N){(!(l.isUndefined(b)||b===null)&&a.call(t,b,l.isString(N)?N.trim():N,p,g))===!0&&j(b,p?p.concat(N):[N])}),f.pop()}}if(!l.isObject(e))throw new TypeError("data must be an object");return j(e),t}function Pe(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Se(e,t){this._pairs=[],e&&ie(e,this,t)}const at=Se.prototype;at.append=function(t,n){this._pairs.push([t,n])};at.toString=function(t){const n=t?function(r){return t.call(this,r,Pe)}:Pe;return this._pairs.map(function(a){return n(a[0])+"="+n(a[1])},"").join("&")};function Is(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function it(e,t,n){if(!t)return e;const r=n&&n.encode||Is;l.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let i;if(a?i=a(t,n):i=l.isURLSearchParams(t)?t.toString():new Se(t,n).toString(r),i){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class ke{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){l.forEach(this.handlers,function(r){r!==null&&t(r)})}}const ot={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},qs=typeof URLSearchParams<"u"?URLSearchParams:Se,Ms=typeof FormData<"u"?FormData:null,$s=typeof Blob<"u"?Blob:null,zs={isBrowser:!0,classes:{URLSearchParams:qs,FormData:Ms,Blob:$s},protocols:["http","https","file","blob","url","data"]},Re=typeof window<"u"&&typeof document<"u",pe=typeof navigator=="object"&&navigator||void 0,Hs=Re&&(!pe||["ReactNative","NativeScript","NS"].indexOf(pe.product)<0),Js=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Vs=Re&&window.location.href||"http://localhost",Ws=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Re,hasStandardBrowserEnv:Hs,hasStandardBrowserWebWorkerEnv:Js,navigator:pe,origin:Vs},Symbol.toStringTag,{value:"Module"})),R={...Ws,...zs};function Ks(e,t){return ie(e,new R.classes.URLSearchParams,Object.assign({visitor:function(n,r,a,i){return R.isNode&&l.isBuffer(n)?(this.append(r,n.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function Gs(e){return l.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Xs(e){const t={},n=Object.keys(e);let r;const a=n.length;let i;for(r=0;r<a;r++)i=n[r],t[i]=e[i];return t}function lt(e){function t(n,r,a,i){let o=n[i++];if(o==="__proto__")return!0;const d=Number.isFinite(+o),h=i>=n.length;return o=!o&&l.isArray(a)?a.length:o,h?(l.hasOwnProp(a,o)?a[o]=[a[o],r]:a[o]=r,!d):((!a[o]||!l.isObject(a[o]))&&(a[o]=[]),t(n,r,a[o],i)&&l.isArray(a[o])&&(a[o]=Xs(a[o])),!d)}if(l.isFormData(e)&&l.isFunction(e.entries)){const n={};return l.forEachEntry(e,(r,a)=>{t(Gs(r),a,n,0)}),n}return null}function Qs(e,t,n){if(l.isString(e))try{return(t||JSON.parse)(e),l.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const V={transitional:ot,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",a=r.indexOf("application/json")>-1,i=l.isObject(t);if(i&&l.isHTMLForm(t)&&(t=new FormData(t)),l.isFormData(t))return a?JSON.stringify(lt(t)):t;if(l.isArrayBuffer(t)||l.isBuffer(t)||l.isStream(t)||l.isFile(t)||l.isBlob(t)||l.isReadableStream(t))return t;if(l.isArrayBufferView(t))return t.buffer;if(l.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let d;if(i){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Ks(t,this.formSerializer).toString();if((d=l.isFileList(t))||r.indexOf("multipart/form-data")>-1){const h=this.env&&this.env.FormData;return ie(d?{"files[]":t}:t,h&&new h,this.formSerializer)}}return i||a?(n.setContentType("application/json",!1),Qs(t)):t}],transformResponse:[function(t){const n=this.transitional||V.transitional,r=n&&n.forcedJSONParsing,a=this.responseType==="json";if(l.isResponse(t)||l.isReadableStream(t))return t;if(t&&l.isString(t)&&(r&&!this.responseType||a)){const o=!(n&&n.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(d){if(o)throw d.name==="SyntaxError"?y.from(d,y.ERR_BAD_RESPONSE,this,null,this.response):d}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:R.classes.FormData,Blob:R.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};l.forEach(["delete","get","head","post","put","patch"],e=>{V.headers[e]={}});const Zs=l.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ys=e=>{const t={};let n,r,a;return e&&e.split(`
`).forEach(function(o){a=o.indexOf(":"),n=o.substring(0,a).trim().toLowerCase(),r=o.substring(a+1).trim(),!(!n||t[n]&&Zs[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},De=Symbol("internals");function $(e){return e&&String(e).trim().toLowerCase()}function G(e){return e===!1||e==null?e:l.isArray(e)?e.map(G):String(e)}function en(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const tn=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function de(e,t,n,r,a){if(l.isFunction(r))return r.call(this,t,n);if(a&&(t=n),!!l.isString(t)){if(l.isString(r))return t.indexOf(r)!==-1;if(l.isRegExp(r))return r.test(t)}}function sn(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function nn(e,t){const n=l.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(a,i,o){return this[r].call(this,t,a,i,o)},configurable:!0})})}let _=class{constructor(t){t&&this.set(t)}set(t,n,r){const a=this;function i(d,h,m){const c=$(h);if(!c)throw new Error("header name must be a non-empty string");const f=l.findKey(a,c);(!f||a[f]===void 0||m===!0||m===void 0&&a[f]!==!1)&&(a[f||h]=G(d))}const o=(d,h)=>l.forEach(d,(m,c)=>i(m,c,h));if(l.isPlainObject(t)||t instanceof this.constructor)o(t,n);else if(l.isString(t)&&(t=t.trim())&&!tn(t))o(Ys(t),n);else if(l.isObject(t)&&l.isIterable(t)){let d={},h,m;for(const c of t){if(!l.isArray(c))throw TypeError("Object iterator must return a key-value pair");d[m=c[0]]=(h=d[m])?l.isArray(h)?[...h,c[1]]:[h,c[1]]:c[1]}o(d,n)}else t!=null&&i(n,t,r);return this}get(t,n){if(t=$(t),t){const r=l.findKey(this,t);if(r){const a=this[r];if(!n)return a;if(n===!0)return en(a);if(l.isFunction(n))return n.call(this,a,r);if(l.isRegExp(n))return n.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=$(t),t){const r=l.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||de(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let a=!1;function i(o){if(o=$(o),o){const d=l.findKey(r,o);d&&(!n||de(r,r[d],d,n))&&(delete r[d],a=!0)}}return l.isArray(t)?t.forEach(i):i(t),a}clear(t){const n=Object.keys(this);let r=n.length,a=!1;for(;r--;){const i=n[r];(!t||de(this,this[i],i,t,!0))&&(delete this[i],a=!0)}return a}normalize(t){const n=this,r={};return l.forEach(this,(a,i)=>{const o=l.findKey(r,i);if(o){n[o]=G(a),delete n[i];return}const d=t?sn(i):String(i).trim();d!==i&&delete n[i],n[d]=G(a),r[d]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return l.forEach(this,(r,a)=>{r!=null&&r!==!1&&(n[a]=t&&l.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(a=>r.set(a)),r}static accessor(t){const r=(this[De]=this[De]={accessors:{}}).accessors,a=this.prototype;function i(o){const d=$(o);r[d]||(nn(a,o),r[d]=!0)}return l.isArray(t)?t.forEach(i):i(t),this}};_.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);l.reduceDescriptors(_.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});l.freezeMethods(_);function ue(e,t){const n=this||V,r=t||n,a=_.from(r.headers);let i=r.data;return l.forEach(e,function(d){i=d.call(n,i,a.normalize(),t?t.status:void 0)}),a.normalize(),i}function ct(e){return!!(e&&e.__CANCEL__)}function I(e,t,n){y.call(this,e??"canceled",y.ERR_CANCELED,t,n),this.name="CanceledError"}l.inherits(I,y,{__CANCEL__:!0});function dt(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new y("Request failed with status code "+n.status,[y.ERR_BAD_REQUEST,y.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function rn(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function an(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a=0,i=0,o;return t=t!==void 0?t:1e3,function(h){const m=Date.now(),c=r[i];o||(o=m),n[a]=h,r[a]=m;let f=i,g=0;for(;f!==a;)g+=n[f++],f=f%e;if(a=(a+1)%e,a===i&&(i=(i+1)%e),m-o<t)return;const j=c&&m-c;return j?Math.round(g*1e3/j):void 0}}function on(e,t){let n=0,r=1e3/t,a,i;const o=(m,c=Date.now())=>{n=c,a=null,i&&(clearTimeout(i),i=null),e.apply(null,m)};return[(...m)=>{const c=Date.now(),f=c-n;f>=r?o(m,c):(a=m,i||(i=setTimeout(()=>{i=null,o(a)},r-f)))},()=>a&&o(a)]}const Z=(e,t,n=3)=>{let r=0;const a=an(50,250);return on(i=>{const o=i.loaded,d=i.lengthComputable?i.total:void 0,h=o-r,m=a(h),c=o<=d;r=o;const f={loaded:o,total:d,progress:d?o/d:void 0,bytes:h,rate:m||void 0,estimated:m&&d&&c?(d-o)/m:void 0,event:i,lengthComputable:d!=null,[t?"download":"upload"]:!0};e(f)},n)},Le=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Fe=e=>(...t)=>l.asap(()=>e(...t)),ln=R.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,R.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(R.origin),R.navigator&&/(msie|trident)/i.test(R.navigator.userAgent)):()=>!0,cn=R.hasStandardBrowserEnv?{write(e,t,n,r,a,i){const o=[e+"="+encodeURIComponent(t)];l.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),l.isString(r)&&o.push("path="+r),l.isString(a)&&o.push("domain="+a),i===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function dn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function un(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function ut(e,t,n){let r=!dn(t);return e&&(r||n==!1)?un(e,t):t}const Ue=e=>e instanceof _?{...e}:e;function U(e,t){t=t||{};const n={};function r(m,c,f,g){return l.isPlainObject(m)&&l.isPlainObject(c)?l.merge.call({caseless:g},m,c):l.isPlainObject(c)?l.merge({},c):l.isArray(c)?c.slice():c}function a(m,c,f,g){if(l.isUndefined(c)){if(!l.isUndefined(m))return r(void 0,m,f,g)}else return r(m,c,f,g)}function i(m,c){if(!l.isUndefined(c))return r(void 0,c)}function o(m,c){if(l.isUndefined(c)){if(!l.isUndefined(m))return r(void 0,m)}else return r(void 0,c)}function d(m,c,f){if(f in t)return r(m,c);if(f in e)return r(void 0,m)}const h={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:d,headers:(m,c,f)=>a(Ue(m),Ue(c),f,!0)};return l.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=h[c]||a,g=f(e[c],t[c],c);l.isUndefined(g)&&f!==d||(n[c]=g)}),n}const mt=e=>{const t=U({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:a,xsrfCookieName:i,headers:o,auth:d}=t;t.headers=o=_.from(o),t.url=it(ut(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),d&&o.set("Authorization","Basic "+btoa((d.username||"")+":"+(d.password?unescape(encodeURIComponent(d.password)):"")));let h;if(l.isFormData(n)){if(R.hasStandardBrowserEnv||R.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((h=o.getContentType())!==!1){const[m,...c]=h?h.split(";").map(f=>f.trim()).filter(Boolean):[];o.setContentType([m||"multipart/form-data",...c].join("; "))}}if(R.hasStandardBrowserEnv&&(r&&l.isFunction(r)&&(r=r(t)),r||r!==!1&&ln(t.url))){const m=a&&i&&cn.read(i);m&&o.set(a,m)}return t},mn=typeof XMLHttpRequest<"u",fn=mn&&function(e){return new Promise(function(n,r){const a=mt(e);let i=a.data;const o=_.from(a.headers).normalize();let{responseType:d,onUploadProgress:h,onDownloadProgress:m}=a,c,f,g,j,x;function p(){j&&j(),x&&x(),a.cancelToken&&a.cancelToken.unsubscribe(c),a.signal&&a.signal.removeEventListener("abort",c)}let u=new XMLHttpRequest;u.open(a.method.toUpperCase(),a.url,!0),u.timeout=a.timeout;function b(){if(!u)return;const w=_.from("getAllResponseHeaders"in u&&u.getAllResponseHeaders()),E={data:!d||d==="text"||d==="json"?u.responseText:u.response,status:u.status,statusText:u.statusText,headers:w,config:e,request:u};dt(function(D){n(D),p()},function(D){r(D),p()},E),u=null}"onloadend"in u?u.onloadend=b:u.onreadystatechange=function(){!u||u.readyState!==4||u.status===0&&!(u.responseURL&&u.responseURL.indexOf("file:")===0)||setTimeout(b)},u.onabort=function(){u&&(r(new y("Request aborted",y.ECONNABORTED,e,u)),u=null)},u.onerror=function(){r(new y("Network Error",y.ERR_NETWORK,e,u)),u=null},u.ontimeout=function(){let P=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const E=a.transitional||ot;a.timeoutErrorMessage&&(P=a.timeoutErrorMessage),r(new y(P,E.clarifyTimeoutError?y.ETIMEDOUT:y.ECONNABORTED,e,u)),u=null},i===void 0&&o.setContentType(null),"setRequestHeader"in u&&l.forEach(o.toJSON(),function(P,E){u.setRequestHeader(E,P)}),l.isUndefined(a.withCredentials)||(u.withCredentials=!!a.withCredentials),d&&d!=="json"&&(u.responseType=a.responseType),m&&([g,x]=Z(m,!0),u.addEventListener("progress",g)),h&&u.upload&&([f,j]=Z(h),u.upload.addEventListener("progress",f),u.upload.addEventListener("loadend",j)),(a.cancelToken||a.signal)&&(c=w=>{u&&(r(!w||w.type?new I(null,e,u):w),u.abort(),u=null)},a.cancelToken&&a.cancelToken.subscribe(c),a.signal&&(a.signal.aborted?c():a.signal.addEventListener("abort",c)));const N=rn(a.url);if(N&&R.protocols.indexOf(N)===-1){r(new y("Unsupported protocol "+N+":",y.ERR_BAD_REQUEST,e));return}u.send(i||null)})},hn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,a;const i=function(m){if(!a){a=!0,d();const c=m instanceof Error?m:this.reason;r.abort(c instanceof y?c:new I(c instanceof Error?c.message:c))}};let o=t&&setTimeout(()=>{o=null,i(new y(`timeout ${t} of ms exceeded`,y.ETIMEDOUT))},t);const d=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(m=>{m.unsubscribe?m.unsubscribe(i):m.removeEventListener("abort",i)}),e=null)};e.forEach(m=>m.addEventListener("abort",i));const{signal:h}=r;return h.unsubscribe=()=>l.asap(d),h}},xn=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,a;for(;r<n;)a=r+t,yield e.slice(r,a),r=a},pn=async function*(e,t){for await(const n of gn(e))yield*xn(n,t)},gn=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Be=(e,t,n,r)=>{const a=pn(e,t);let i=0,o,d=h=>{o||(o=!0,r&&r(h))};return new ReadableStream({async pull(h){try{const{done:m,value:c}=await a.next();if(m){d(),h.close();return}let f=c.byteLength;if(n){let g=i+=f;n(g)}h.enqueue(new Uint8Array(c))}catch(m){throw d(m),m}},cancel(h){return d(h),a.return()}},{highWaterMark:2})},oe=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ft=oe&&typeof ReadableStream=="function",yn=oe&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ht=(e,...t)=>{try{return!!e(...t)}catch{return!1}},bn=ft&&ht(()=>{let e=!1;const t=new Request(R.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ie=64*1024,ge=ft&&ht(()=>l.isReadableStream(new Response("").body)),Y={stream:ge&&(e=>e.body)};oe&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Y[t]&&(Y[t]=l.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new y(`Response type '${t}' is not supported`,y.ERR_NOT_SUPPORT,r)})})})(new Response);const jn=async e=>{if(e==null)return 0;if(l.isBlob(e))return e.size;if(l.isSpecCompliantForm(e))return(await new Request(R.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(l.isArrayBufferView(e)||l.isArrayBuffer(e))return e.byteLength;if(l.isURLSearchParams(e)&&(e=e+""),l.isString(e))return(await yn(e)).byteLength},Nn=async(e,t)=>{const n=l.toFiniteNumber(e.getContentLength());return n??jn(t)},wn=oe&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:i,timeout:o,onDownloadProgress:d,onUploadProgress:h,responseType:m,headers:c,withCredentials:f="same-origin",fetchOptions:g}=mt(e);m=m?(m+"").toLowerCase():"text";let j=hn([a,i&&i.toAbortSignal()],o),x;const p=j&&j.unsubscribe&&(()=>{j.unsubscribe()});let u;try{if(h&&bn&&n!=="get"&&n!=="head"&&(u=await Nn(c,r))!==0){let E=new Request(t,{method:"POST",body:r,duplex:"half"}),k;if(l.isFormData(r)&&(k=E.headers.get("content-type"))&&c.setContentType(k),E.body){const[D,W]=Le(u,Z(Fe(h)));r=Be(E.body,Ie,D,W)}}l.isString(f)||(f=f?"include":"omit");const b="credentials"in Request.prototype;x=new Request(t,{...g,signal:j,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:b?f:void 0});let N=await fetch(x,g);const w=ge&&(m==="stream"||m==="response");if(ge&&(d||w&&p)){const E={};["status","statusText","headers"].forEach(Ae=>{E[Ae]=N[Ae]});const k=l.toFiniteNumber(N.headers.get("content-length")),[D,W]=d&&Le(k,Z(Fe(d),!0))||[];N=new Response(Be(N.body,Ie,D,()=>{W&&W(),p&&p()}),E)}m=m||"text";let P=await Y[l.findKey(Y,m)||"text"](N,e);return!w&&p&&p(),await new Promise((E,k)=>{dt(E,k,{data:P,headers:_.from(N.headers),status:N.status,statusText:N.statusText,config:e,request:x})})}catch(b){throw p&&p(),b&&b.name==="TypeError"&&/Load failed|fetch/i.test(b.message)?Object.assign(new y("Network Error",y.ERR_NETWORK,e,x),{cause:b.cause||b}):y.from(b,b&&b.code,e,x)}}),ye={http:Fs,xhr:fn,fetch:wn};l.forEach(ye,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const qe=e=>`- ${e}`,vn=e=>l.isFunction(e)||e===null||e===!1,xt={getAdapter:e=>{e=l.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let i=0;i<t;i++){n=e[i];let o;if(r=n,!vn(n)&&(r=ye[(o=String(n)).toLowerCase()],r===void 0))throw new y(`Unknown adapter '${o}'`);if(r)break;a[o||"#"+i]=r}if(!r){const i=Object.entries(a).map(([d,h])=>`adapter ${d} `+(h===!1?"is not supported by the environment":"is not available in the build"));let o=t?i.length>1?`since :
`+i.map(qe).join(`
`):" "+qe(i[0]):"as no adapter specified";throw new y("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return r},adapters:ye};function me(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new I(null,e)}function Me(e){return me(e),e.headers=_.from(e.headers),e.data=ue.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),xt.getAdapter(e.adapter||V.adapter)(e).then(function(r){return me(e),r.data=ue.call(e,e.transformResponse,r),r.headers=_.from(r.headers),r},function(r){return ct(r)||(me(e),r&&r.response&&(r.response.data=ue.call(e,e.transformResponse,r.response),r.response.headers=_.from(r.response.headers))),Promise.reject(r)})}const pt="1.10.0",le={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{le[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const $e={};le.transitional=function(t,n,r){function a(i,o){return"[Axios v"+pt+"] Transitional option '"+i+"'"+o+(r?". "+r:"")}return(i,o,d)=>{if(t===!1)throw new y(a(o," has been removed"+(n?" in "+n:"")),y.ERR_DEPRECATED);return n&&!$e[o]&&($e[o]=!0,console.warn(a(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(i,o,d):!0}};le.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Sn(e,t,n){if(typeof e!="object")throw new y("options must be an object",y.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const i=r[a],o=t[i];if(o){const d=e[i],h=d===void 0||o(d,i,e);if(h!==!0)throw new y("option "+i+" must be "+h,y.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new y("Unknown option "+i,y.ERR_BAD_OPTION)}}const X={assertOptions:Sn,validators:le},O=X.validators;let F=class{constructor(t){this.defaults=t||{},this.interceptors={request:new ke,response:new ke}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const i=a.stack?a.stack.replace(/^.+\n/,""):"";try{r.stack?i&&!String(r.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+i):r.stack=i}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=U(this.defaults,n);const{transitional:r,paramsSerializer:a,headers:i}=n;r!==void 0&&X.assertOptions(r,{silentJSONParsing:O.transitional(O.boolean),forcedJSONParsing:O.transitional(O.boolean),clarifyTimeoutError:O.transitional(O.boolean)},!1),a!=null&&(l.isFunction(a)?n.paramsSerializer={serialize:a}:X.assertOptions(a,{encode:O.function,serialize:O.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),X.assertOptions(n,{baseUrl:O.spelling("baseURL"),withXsrfToken:O.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=i&&l.merge(i.common,i[n.method]);i&&l.forEach(["delete","get","head","post","put","patch","common"],x=>{delete i[x]}),n.headers=_.concat(o,i);const d=[];let h=!0;this.interceptors.request.forEach(function(p){typeof p.runWhen=="function"&&p.runWhen(n)===!1||(h=h&&p.synchronous,d.unshift(p.fulfilled,p.rejected))});const m=[];this.interceptors.response.forEach(function(p){m.push(p.fulfilled,p.rejected)});let c,f=0,g;if(!h){const x=[Me.bind(this),void 0];for(x.unshift.apply(x,d),x.push.apply(x,m),g=x.length,c=Promise.resolve(n);f<g;)c=c.then(x[f++],x[f++]);return c}g=d.length;let j=n;for(f=0;f<g;){const x=d[f++],p=d[f++];try{j=x(j)}catch(u){p.call(this,u);break}}try{c=Me.call(this,j)}catch(x){return Promise.reject(x)}for(f=0,g=m.length;f<g;)c=c.then(m[f++],m[f++]);return c}getUri(t){t=U(this.defaults,t);const n=ut(t.baseURL,t.url,t.allowAbsoluteUrls);return it(n,t.params,t.paramsSerializer)}};l.forEach(["delete","get","head","options"],function(t){F.prototype[t]=function(n,r){return this.request(U(r||{},{method:t,url:n,data:(r||{}).data}))}});l.forEach(["post","put","patch"],function(t){function n(r){return function(i,o,d){return this.request(U(d||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}F.prototype[t]=n(),F.prototype[t+"Form"]=n(!0)});let Rn=class gt{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(i){n=i});const r=this;this.promise.then(a=>{if(!r._listeners)return;let i=r._listeners.length;for(;i-- >0;)r._listeners[i](a);r._listeners=null}),this.promise.then=a=>{let i;const o=new Promise(d=>{r.subscribe(d),i=d}).then(a);return o.cancel=function(){r.unsubscribe(i)},o},t(function(i,o,d){r.reason||(r.reason=new I(i,o,d),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new gt(function(a){t=a}),cancel:t}}};function En(e){return function(n){return e.apply(null,n)}}function An(e){return l.isObject(e)&&e.isAxiosError===!0}const be={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(be).forEach(([e,t])=>{be[t]=e});function yt(e){const t=new F(e),n=Ke(F.prototype.request,t);return l.extend(n,F.prototype,t,{allOwnKeys:!0}),l.extend(n,t,null,{allOwnKeys:!0}),n.create=function(a){return yt(U(e,a))},n}const S=yt(V);S.Axios=F;S.CanceledError=I;S.CancelToken=Rn;S.isCancel=ct;S.VERSION=pt;S.toFormData=ie;S.AxiosError=y;S.Cancel=S.CanceledError;S.all=function(t){return Promise.all(t)};S.spread=En;S.isAxiosError=An;S.mergeConfig=U;S.AxiosHeaders=_;S.formToJSON=e=>lt(l.isHTMLForm(e)?new FormData(e):e);S.getAdapter=xt.getAdapter;S.HttpStatusCode=be;S.default=S;const{Axios:In,AxiosError:qn,CanceledError:Mn,isCancel:$n,CancelToken:zn,VERSION:Hn,all:Jn,Cancel:Vn,isAxiosError:Wn,spread:Kn,toFormData:Gn,AxiosHeaders:Xn,HttpStatusCode:Qn,formToJSON:Zn,getAdapter:Yn,mergeConfig:er}=S,_n="http://localhost:8000",Ee=S.create({baseURL:`${_n}/api/v1`,timeout:3e4,headers:{"Content-Type":"application/json"}});Ee.interceptors.request.use(e=>{const t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));Ee.interceptors.response.use(e=>e,e=>{var t,n,r;return((t=e.response)==null?void 0:t.status)===401?(localStorage.removeItem("auth_token"),T.error("Authentication required")):((n=e.response)==null?void 0:n.status)===403?T.error("Access denied"):((r=e.response)==null?void 0:r.status)>=500?T.error("Server error occurred"):e.code==="ECONNABORTED"?T.error("Request timeout"):e.response||T.error("Network error - check connection"),Promise.reject(e)});const Tn=()=>{const[e,t]=v.useState(!1),[n,r]=v.useState(null),a=v.useCallback(async(m,c,f=null,g={})=>{t(!0),r(null);try{return await Ee({method:m,url:c,data:f,...g})}catch(j){throw r(j),j}finally{t(!1)}},[]),i=v.useCallback((m,c={})=>a("GET",m,null,c),[a]),o=v.useCallback((m,c,f={})=>a("POST",m,c,f),[a]),d=v.useCallback((m,c,f={})=>a("PUT",m,c,f),[a]),h=v.useCallback((m,c={})=>a("DELETE",m,null,c),[a]);return{loading:e,error:n,get:i,post:o,put:d,delete:h,request:a}},Cn=()=>{const{jobId:e}=ze(),t=je();Tn();const[n,r]=v.useState({status:"processing",progress:0,currentStep:"Initializing...",steps:[],estimatedTime:60}),[a]=v.useState(Date.now()),[i,o]=v.useState(0),d=()=>[{id:"upload",name:"Document Upload",status:"completed"},{id:"validation",name:"File Validation",status:"completed"},{id:"ocr",name:"Document Processing",status:"processing"},{id:"classification",name:"Document Classification",status:"pending"},{id:"evidence",name:"Evidence Extraction",status:"pending"},{id:"liability",name:"Liability Assessment",status:"pending"},{id:"confidence",name:"Confidence Scoring",status:"pending"},{id:"analysis",name:"AI Analysis",status:"pending"},{id:"results",name:"Generate Results",status:"pending"}];v.useEffect(()=>{r(f=>({...f,steps:d()}));const c=setInterval(()=>{o(Date.now()-a),r(f=>{var u;const g=Math.min(f.progress+Math.random()*15,95),j=Math.floor(g/100*f.steps.length),x=f.steps.map((b,N)=>N<j?{...b,status:"completed"}:N===j?{...b,status:"processing"}:b),p=((u=x[j])==null?void 0:u.name)||"Finalizing...";return g>=95&&f.status==="processing"?(setTimeout(()=>{T.success("Processing completed successfully!"),t(`/results/${e}`)},2e3),{...f,progress:100,status:"completed",currentStep:"Processing Complete",steps:x.map(b=>({...b,status:"completed"}))}):{...f,progress:g,currentStep:p,steps:x}})},1500);return()=>clearInterval(c)},[e,t,a]);const h=c=>{const f=Math.floor(c/1e3),g=Math.floor(f/60),j=f%60;return`${g}:${j.toString().padStart(2,"0")}`},m=c=>{switch(c){case"completed":return s.jsx(Q,{className:"h-5 w-5 text-green-500"});case"processing":return s.jsx("div",{className:"loading-spinner"});case"error":return s.jsx(ee,{className:"h-5 w-5 text-red-500"});default:return s.jsx("div",{className:"h-5 w-5 rounded-full border-2 border-gray-300"})}};return s.jsxs("div",{className:"max-w-4xl mx-auto space-y-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Processing Documents"}),s.jsx("p",{className:"text-gray-600",children:"AI-powered liability analysis in progress"})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Processing Progress"}),s.jsxs("div",{className:"text-sm text-gray-500",children:["Job ID: ",e]})]}),s.jsxs("div",{className:"mb-6",children:[s.jsxs("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[s.jsx("span",{children:n.currentStep}),s.jsxs("span",{children:[Math.round(n.progress),"%"]})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:s.jsx("div",{className:"bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out",style:{width:`${n.progress}%`}})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[s.jsx(we,{className:"h-6 w-6 text-blue-600 mx-auto mb-2"}),s.jsx("div",{className:"text-lg font-semibold text-blue-900",children:h(i)}),s.jsx("div",{className:"text-sm text-blue-600",children:"Elapsed Time"})]}),s.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[s.jsx(St,{className:"h-6 w-6 text-green-600 mx-auto mb-2"}),s.jsx("div",{className:"text-lg font-semibold text-green-900",children:"Advanced"}),s.jsx("div",{className:"text-sm text-green-600",children:"Processing Engine"})]}),s.jsxs("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[s.jsx(Ne,{className:"h-6 w-6 text-purple-600 mx-auto mb-2"}),s.jsx("div",{className:"text-lg font-semibold text-purple-900",children:n.steps.filter(c=>c.status==="completed").length}),s.jsx("div",{className:"text-sm text-purple-600",children:"Steps Completed"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Processing Steps"}),s.jsx("div",{className:"space-y-4",children:n.steps.map((c,f)=>s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"flex-shrink-0",children:m(c.status)}),s.jsxs("div",{className:"flex-1",children:[s.jsx("div",{className:`font-medium ${c.status==="completed"?"text-green-700":c.status==="processing"?"text-blue-700":c.status==="error"?"text-red-700":"text-gray-500"}`,children:c.name}),c.status==="processing"&&s.jsx("div",{className:"text-sm text-blue-600 mt-1",children:"Currently processing..."}),c.status==="completed"&&s.jsx("div",{className:"text-sm text-green-600 mt-1",children:"✓ Completed"})]}),f<n.steps.length-1&&s.jsx(Rt,{className:"h-4 w-4 text-gray-400"})]},c.id))})]}),s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Live Updates"}),s.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[s.jsx("div",{children:"• Advanced document processing in progress"}),s.jsx("div",{children:"• Document classification and analysis"}),s.jsx("div",{children:"• AI-powered evidence extraction"}),s.jsx("div",{children:"• Extracting liability evidence"}),s.jsx("div",{children:"• Calculating fault percentages"}),s.jsx("div",{children:"• Generating confidence scores"})]})]}),s.jsx("div",{className:"text-center",children:s.jsx("button",{onClick:()=>t("/"),className:"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"Return to Dashboard"})})]})},On=({results:e,selectedUseCase:t})=>{var i,o,d,h,m,c,f,g,j,x;const[n,r]=v.useState({dataStatus:!0,legalAnalysis:!1,lossQuantum:!1,communications:!1,workflow:!1}),a=p=>{r(u=>({...u,[p]:!u[p]}))};return!e||t!=="uc05"?null:s.jsxs("div",{className:"space-y-6",children:[e.data_sufficiency&&s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border",children:[s.jsx("div",{className:"p-6 cursor-pointer",onClick:()=>a("dataStatus"),children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Et,{className:`h-6 w-6 ${e.data_sufficiency.level==="complete"?"text-green-600":e.data_sufficiency.level==="sufficient"?"text-yellow-600":"text-red-600"}`}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Data Sufficiency Assessment"}),s.jsxs("p",{className:"text-sm text-gray-600",children:["Status: ",e.data_sufficiency.level.replace("_"," ").toUpperCase()]})]})]}),n.dataStatus?s.jsx(q,{}):s.jsx(M,{})]})}),n.dataStatus&&s.jsxs("div",{className:"px-6 pb-6 border-t border-gray-100",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[s.jsxs("div",{className:"text-2xl font-bold text-gray-900",children:[Math.round(e.data_sufficiency.confidence_score*100),"%"]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Confidence Score"})]}),s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-gray-900",children:((i=e.data_sufficiency.missing_documents)==null?void 0:i.length)||0}),s.jsx("div",{className:"text-sm text-gray-600",children:"Missing Documents"})]})]}),((o=e.data_sufficiency.missing_documents)==null?void 0:o.length)>0&&s.jsxs("div",{className:"mt-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Missing Documents:"}),s.jsx("ul",{className:"space-y-1",children:e.data_sufficiency.missing_documents.map((p,u)=>s.jsxs("li",{className:"flex items-center space-x-2",children:[s.jsx(ee,{className:"h-4 w-4 text-red-500"}),s.jsx("span",{className:"text-gray-700",children:p.document_type.replace("_"," ")}),s.jsxs("span",{className:"text-sm text-gray-500",children:["(",p.importance,")"]})]},u))})]})]})]}),e.legal_analysis&&s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border",children:[s.jsx("div",{className:"p-6 cursor-pointer",onClick:()=>a("legalAnalysis"),children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(At,{className:"h-6 w-6 text-blue-600"}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Legal Precedent Analysis"}),s.jsxs("p",{className:"text-sm text-gray-600",children:[((d=e.legal_analysis.applicable_precedents)==null?void 0:d.length)||0," precedents found"]})]})]}),n.legalAnalysis?s.jsx(q,{}):s.jsx(M,{})]})}),n.legalAnalysis&&s.jsx("div",{className:"px-6 pb-6 border-t border-gray-100",children:s.jsxs("div",{className:"mt-4 space-y-4",children:[(h=e.legal_analysis.applicable_precedents)==null?void 0:h.map((p,u)=>s.jsx("div",{className:"bg-blue-50 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start justify-between",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-blue-900",children:p.case_name}),s.jsx("p",{className:"text-sm text-blue-700",children:p.legal_principle}),s.jsxs("div",{className:"mt-2 text-sm text-blue-600",children:["Relevance: ",Math.round(p.relevance_score*100),"%"]})]}),s.jsx(_t,{className:"h-4 w-4 text-blue-600"})]})},u)),e.legal_analysis.legal_reasoning&&s.jsxs("div",{className:"mt-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Legal Reasoning:"}),s.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:s.jsx("pre",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:e.legal_analysis.legal_reasoning})})]})]})})]}),e.loss_quantum&&s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border",children:[s.jsx("div",{className:"p-6 cursor-pointer",onClick:()=>a("lossQuantum"),children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Tt,{className:"h-6 w-6 text-green-600"}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Loss Quantum Calculation"}),s.jsxs("p",{className:"text-sm text-gray-600",children:["Settlement: $",(m=e.loss_quantum.settlement_recommendation)==null?void 0:m.toLocaleString()]})]})]}),n.lossQuantum?s.jsx(q,{}):s.jsx(M,{})]})}),n.lossQuantum&&s.jsxs("div",{className:"px-6 pb-6 border-t border-gray-100",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4",children:[s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[s.jsxs("div",{className:"text-xl font-bold text-gray-900",children:["$",(c=e.loss_quantum.total_damages)==null?void 0:c.toLocaleString()]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Total Damages"})]}),s.jsxs("div",{className:"bg-red-50 rounded-lg p-4 text-center",children:[s.jsxs("div",{className:"text-xl font-bold text-red-600",children:["$",(f=e.loss_quantum.insured_liability)==null?void 0:f.toLocaleString()]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Insured Liability"})]}),s.jsxs("div",{className:"bg-green-50 rounded-lg p-4 text-center",children:[s.jsxs("div",{className:"text-xl font-bold text-green-600",children:["$",(g=e.loss_quantum.settlement_recommendation)==null?void 0:g.toLocaleString()]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Recommended Settlement"})]})]}),e.loss_quantum.damage_breakdown&&s.jsxs("div",{className:"mt-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Damage Breakdown:"}),s.jsx("div",{className:"space-y-2",children:e.loss_quantum.damage_breakdown.map((p,u)=>{var b;return s.jsxs("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[s.jsx("span",{className:"text-gray-700",children:p.description}),s.jsxs("span",{className:"font-medium",children:["$",(b=p.amount)==null?void 0:b.toLocaleString()]})]},u)})})]})]})]}),e.email_drafts&&e.email_drafts.length>0&&s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border",children:[s.jsx("div",{className:"p-6 cursor-pointer",onClick:()=>a("communications"),children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Ct,{className:"h-6 w-6 text-purple-600"}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Generated Communications"}),s.jsxs("p",{className:"text-sm text-gray-600",children:[e.email_drafts.length," email draft(s) ready"]})]})]}),n.communications?s.jsx(q,{}):s.jsx(M,{})]})}),n.communications&&s.jsx("div",{className:"px-6 pb-6 border-t border-gray-100",children:s.jsx("div",{className:"space-y-4 mt-4",children:e.email_drafts.map((p,u)=>s.jsxs("div",{className:"bg-purple-50 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-start justify-between mb-2",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-purple-900",children:p.subject}),s.jsxs("p",{className:"text-sm text-purple-700",children:["To: ",p.recipient_type," • Urgency: ",p.urgency]})]}),s.jsx("button",{className:"text-purple-600 hover:text-purple-800",children:s.jsx(Ot,{className:"h-4 w-4"})})]}),s.jsx("div",{className:"bg-white rounded p-3 text-sm text-gray-700",children:s.jsxs("pre",{className:"whitespace-pre-wrap",children:[p.body.substring(0,200),"..."]})}),s.jsxs("div",{className:"mt-2 text-xs text-purple-600",children:["Follow-up in ",p.follow_up_days," days"]})]},u))})})]}),e.workflow_state&&s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border",children:[s.jsx("div",{className:"p-6 cursor-pointer",onClick:()=>a("workflow"),children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Pt,{className:"h-6 w-6 text-indigo-600"}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Workflow Status"}),s.jsxs("p",{className:"text-sm text-gray-600",children:["Current stage: ",(j=e.workflow_state.current_stage)==null?void 0:j.replace("_"," ")]})]})]}),n.workflow?s.jsx(q,{}):s.jsx(M,{})]})}),n.workflow&&s.jsxs("div",{className:"px-6 pb-6 border-t border-gray-100",children:[s.jsxs("div",{className:"mt-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Next Steps:"}),s.jsx("ul",{className:"space-y-2",children:(x=e.next_steps)==null?void 0:x.map((p,u)=>s.jsxs("li",{className:"flex items-center space-x-2",children:[s.jsx(we,{className:"h-4 w-4 text-indigo-600"}),s.jsx("span",{className:"text-gray-700",children:p})]},u))})]}),e.workflow_state.estimated_completion&&s.jsx("div",{className:"mt-4 bg-indigo-50 rounded-lg p-3",children:s.jsxs("div",{className:"text-sm text-indigo-700",children:["Estimated completion: ",new Date(e.workflow_state.estimated_completion).toLocaleString()]})})]})]})]})},Pn=()=>{const{jobId:e}=ze(),t=je(),[n,r]=v.useState(null),[a,i]=v.useState(!0);v.useEffect(()=>{(async()=>{i(!0),await new Promise(g=>setTimeout(g,1500));const f=o("uc05",e);r(f),i(!1)})()},[e]);const o=(c,f)=>({...{jobId:f,timestamp:new Date().toISOString(),processingTime:"2.3s",confidence:.95,status:"completed"},useCase:"Liability Decisions",decision:{primaryLiability:"Party A",faultPercentage:{partyA:75,partyB:25},confidence:.92,reasoning:["Traffic violation detected in Party A documentation","Witness statements support Party B version of events","Physical evidence indicates Party A was speeding","Party A failed to yield right of way"]},evidence:{documentsAnalyzed:8,keyFindings:["Police report indicates Party A ran red light","Damage patterns consistent with high-speed impact","Witness testimony corroborates Party B account","Traffic camera footage supports findings"]},recommendations:["Proceed with 75/25 fault allocation","Request additional medical documentation","Consider settlement negotiations","Monitor for potential appeals"],data_sufficiency:{level:"sufficient",confidence_score:.85,missing_documents:[{document_type:"medical_report",importance:"important"},{document_type:"witness_statement",importance:"optional"}]},legal_analysis:{applicable_precedents:[{case_name:"Thompson v. Metro Grocery Ltd. (2023)",legal_principle:"Occupier's liability for known hazards",relevance_score:.95},{case_name:"Wilson v. Pacific Shopping Centre (2022)",legal_principle:"Reasonable care in winter maintenance",relevance_score:.85}],legal_reasoning:"Based on Canadian comparative negligence law and similar premises liability cases, the fault allocation is supported by strong legal precedent."},loss_quantum:{total_damages:15e3,insured_liability:11250,settlement_recommendation:9500,damage_breakdown:[{description:"Medical expenses",amount:5e3},{description:"Lost wages",amount:3e3},{description:"Pain and suffering",amount:7e3}]},email_drafts:[{subject:"Additional Medical Documentation Required - Claim #UC05-2024-001",body:`Dear Valued Customer,

Thank you for submitting your insurance claim. To complete our assessment, we require additional medical documentation...`,recipient_type:"claimant",urgency:"medium",follow_up_days:10}],workflow_state:{current_stage:"liability_assessment",estimated_completion:new Date(Date.now()+3*24*60*60*1e3).toISOString()},next_steps:["Request additional medical documentation","Proceed with liability analysis","Generate settlement offer","Schedule follow-up review"]}),d=()=>{T.success("Results downloaded successfully")},h=()=>{navigator.clipboard.writeText(window.location.href),T.success("Results link copied to clipboard")},m=()=>{t("/")};return a?s.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[s.jsx("div",{className:"loading-spinner mx-auto mb-4"}),s.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading Results..."}),s.jsxs("p",{className:"text-gray-600",children:["Finalizing analysis for job ",e]})]}):n?s.jsxs("div",{className:"max-w-6xl mx-auto space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("button",{onClick:()=>t("/"),className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",children:s.jsx(Dt,{className:"h-6 w-6"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Analysis Results"}),s.jsxs("p",{className:"text-gray-600",children:[n.useCase," • Job ID: ",e]})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsxs("button",{onClick:h,className:"btn-secondary flex items-center space-x-2",children:[s.jsx(Lt,{className:"h-4 w-4"}),s.jsx("span",{children:"Share"})]}),s.jsxs("button",{onClick:d,className:"btn-primary flex items-center space-x-2",children:[s.jsx(Ft,{className:"h-4 w-4"}),s.jsx("span",{children:"Download"})]})]})]}),s.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Q,{className:"h-6 w-6 text-green-600"}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-semibold text-green-900",children:"Analysis Completed Successfully"}),s.jsxs("p",{className:"text-green-700",children:["Processed in ",n.processingTime," with ",Math.round(n.confidence*100),"% confidence"]})]})]})}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[n.decision&&s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[s.jsx(Ut,{className:"h-6 w-6 text-blue-600"}),s.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Liability Decision"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"bg-blue-50 rounded-lg p-4",children:[s.jsx("h3",{className:"font-semibold text-blue-900 mb-2",children:"Fault Allocation"}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{children:"Party A"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-red-500 h-2 rounded-full",style:{width:`${n.decision.faultPercentage.partyA}%`}})}),s.jsxs("span",{className:"font-semibold text-red-600",children:[n.decision.faultPercentage.partyA,"%"]})]})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{children:"Party B"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${n.decision.faultPercentage.partyB}%`}})}),s.jsxs("span",{className:"font-semibold text-green-600",children:[n.decision.faultPercentage.partyB,"%"]})]})]})]})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Reasoning"}),s.jsx("ul",{className:"space-y-1",children:n.decision.reasoning.map((c,f)=>s.jsxs("li",{className:"flex items-start space-x-2",children:[s.jsx("span",{className:"text-blue-600 mt-1",children:"•"}),s.jsx("span",{className:"text-gray-700",children:c})]},f))})]})]})]}),n.evidence&&s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[s.jsx(Bt,{className:"h-6 w-6 text-green-600"}),s.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Evidence Analysis"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[s.jsx("div",{className:"text-2xl font-bold text-gray-900",children:n.evidence.documentsAnalyzed}),s.jsx("div",{className:"text-sm text-gray-600",children:"Documents Analyzed"})]}),s.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[s.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[Math.round(n.confidence*100),"%"]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Confidence Score"})]})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Key Findings"}),s.jsx("ul",{className:"space-y-2",children:n.evidence.keyFindings.map((c,f)=>s.jsxs("li",{className:"flex items-start space-x-2",children:[s.jsx(Q,{className:"h-4 w-4 text-green-500 mt-0.5 flex-shrink-0"}),s.jsx("span",{className:"text-gray-700",children:c})]},f))})]})]})]}),n.recommendations&&s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[s.jsx(z,{className:"h-6 w-6 text-purple-600"}),s.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Recommendations"})]}),s.jsx("ul",{className:"space-y-3",children:n.recommendations.map((c,f)=>s.jsxs("li",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"bg-purple-100 text-purple-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium flex-shrink-0",children:f+1}),s.jsx("span",{className:"text-gray-700",children:c})]},f))})]}),s.jsx(On,{results:n,selectedUseCase:"uc05"})]}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Processing Summary"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"Status"}),s.jsx("span",{className:"font-medium text-green-600",children:"Completed"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"Processing Time"}),s.jsx("span",{className:"font-medium",children:n.processingTime})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"Confidence"}),s.jsxs("span",{className:"font-medium",children:[Math.round(n.confidence*100),"%"]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"Timestamp"}),s.jsx("span",{className:"font-medium text-sm",children:new Date(n.timestamp).toLocaleString()})]})]})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actions"}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("button",{onClick:m,className:"w-full btn-primary",children:"New Analysis"}),s.jsx("button",{onClick:d,className:"w-full btn-secondary",children:"Export Results"}),s.jsx("button",{onClick:()=>window.print(),className:"w-full btn-secondary",children:"Print Report"})]})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(we,{className:"h-4 w-4 text-blue-600"}),s.jsx("span",{className:"text-gray-600",children:"Speed"})]}),s.jsx("span",{className:"font-medium text-green-600",children:"Excellent"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(It,{className:"h-4 w-4 text-green-600"}),s.jsx("span",{className:"text-gray-600",children:"Accuracy"})]}),s.jsx("span",{className:"font-medium text-green-600",children:"High"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(Ne,{className:"h-4 w-4 text-purple-600"}),s.jsx("span",{className:"text-gray-600",children:"Coverage"})]}),s.jsx("span",{className:"font-medium text-green-600",children:"Complete"})]})]})]})]})]})]}):s.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[s.jsx(kt,{className:"h-12 w-12 text-yellow-500 mx-auto mb-4"}),s.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Results Not Found"}),s.jsxs("p",{className:"text-gray-600 mb-4",children:["Could not load results for job ",e]}),s.jsx("button",{onClick:()=>t("/"),className:"btn-primary",children:"Return to Dashboard"})]})};function kn(){const[e,t]=v.useState(!1);return s.jsx(Nt,{children:s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(qt,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#10B981",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#EF4444",secondary:"#fff"}}}}),s.jsx(Wt,{onMenuClick:()=>t(!e)}),s.jsxs("div",{className:"flex",children:[s.jsx(Kt,{isOpen:e,onClose:()=>t(!1)}),s.jsx("main",{className:`flex-1 transition-all duration-300 ${e?"ml-64":"ml-0"}`,children:s.jsx("div",{className:"p-6",children:s.jsxs(wt,{children:[s.jsx(ce,{path:"/",element:s.jsx(Xt,{})}),s.jsx(ce,{path:"/processing/:jobId",element:s.jsx(Cn,{})}),s.jsx(ce,{path:"/results/:resultId",element:s.jsx(Pn,{})})]})})})]})]})})}fe.createRoot(document.getElementById("root")).render(s.jsx(jt.StrictMode,{children:s.jsx(kn,{})}));
