"""
OCR Service - Simplified Demo Version
"""

import asyncio
import logging
from typing import Dict, List, Optional, Union, Any
from pathlib import Path
import random

logger = logging.getLogger(__name__)

class OCRService:
    """Simplified OCR service for demo purposes"""
    
    def __init__(self):
        self.device = "cpu"
        logger.info(f"OCR Service initialized (demo mode)")
    
    async def warm_up(self):
        """Initialize OCR service"""
        logger.info("✅ OCR service ready (demo mode)")
        return True
    
    async def extract_text(
        self,
        image: Union[str, Path, bytes],
        engine: str = "auto",
        languages: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Extract text from image using mock OCR"""
        try:
            # Simulate processing delay
            await asyncio.sleep(random.uniform(0.5, 2.0))
            
            # Generate mock OCR results
            filename = str(image) if isinstance(image, (str, Path)) else "uploaded_file"
            
            # Mock text content
            mock_text = """
POLICE INCIDENT REPORT
Report #: 2024-001234
Date: January 15, 2024
Location: Main St & Oak Ave

INCIDENT DESCRIPTION:
Motor vehicle collision between two vehicles.
Vehicle 1 (Blue Honda) failed to stop at red light.
Vehicle 2 (White Toyota) had right of way.

WITNESS STATEMENTS:
- <PERSON>: "The blue car ran the red light"
- Mary Johnson: "The white car had the green light"

OFFICER NOTES:
Driver of Vehicle 1 cited for running red light.
Both vehicles sustained moderate damage.
"""
            
            confidence = random.uniform(0.85, 0.98)
            
            return {
                "text": mock_text.strip(),
                "confidence": confidence,
                "engine": "demo-ocr",
                "metadata": {
                    "filename": filename,
                    "processing_time": random.uniform(0.5, 2.0),
                    "language": "en"
                }
            }
            
        except Exception as e:
            logger.error(f"❌ OCR extraction failed: {e}")
            return {
                "text": "",
                "confidence": 0.0,
                "engine": "none",
                "error": str(e)
            }
