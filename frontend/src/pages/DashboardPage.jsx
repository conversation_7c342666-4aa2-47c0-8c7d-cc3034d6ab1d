import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, FileText, Zap, Target, Award } from 'lucide-react';
import toast from 'react-hot-toast';
import { useApi } from '../hooks/useApi';

// Components
import FileUpload from '../components/FileUpload';

const DashboardPage = () => {
  const navigate = useNavigate();
  const { post } = useApi();
  const [isUploading, setIsUploading] = useState(false);

  const handleFileUpload = async (files) => {
    if (!files || files.length === 0) {
      toast.error('Please select files to upload');
      return;
    }

    setIsUploading(true);

    try {
      toast.loading('Uploading files...', { id: 'upload' });

      // Create FormData for file upload
      const formData = new FormData();

      // Add files to FormData
      Array.from(files).forEach((file, index) => {
        formData.append('files', file);
      });

      // Add use case information
      formData.append('use_case', 'uc05');
      formData.append('description', 'Claims Liability Decisions Canada');

      console.log('Uploading files:', files);
      console.log('FormData entries:', Array.from(formData.entries()));

      // Upload files to backend
      const response = await post('/documents/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('Upload response:', response.data);

      const jobId = response.data.job_id;

      if (!jobId) {
        throw new Error('No job ID returned from server');
      }

      toast.success(`Files uploaded successfully! Starting processing...`, { id: 'upload' });

      // Navigate to processing page
      navigate(`/processing/${jobId}`);

    } catch (error) {
      console.error('Upload failed:', error);
      toast.error(`Upload failed: ${error.response?.data?.detail || error.message}`, { id: 'upload' });
    } finally {
      setIsUploading(false);
    }
  };

  const currentUseCase = {
    name: 'Liability Decisions',
    description: 'Intelligent fault assessment and liability analysis'
  };

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg text-white p-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">R</span>
              </div>
              <h1 className="text-3xl font-bold">Rozie AI</h1>
            </div>
            <p className="text-blue-100 text-lg mb-4">
              Liability Intelligence Platform
            </p>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span className="font-medium">{currentUseCase.name}</span>
              </div>
              <div className="text-blue-200">•</div>
              <div className="text-blue-200">{currentUseCase.description}</div>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="text-right">
              <div className="text-2xl font-bold">95%</div>
              <div className="text-blue-200">Accuracy Rate</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - File Upload */}
        <div className="lg:col-span-2 space-y-6">
          {/* File Upload Section */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Upload className="h-6 w-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">
                Upload Documents
              </h2>
            </div>
            
            <FileUpload
              onUpload={handleFileUpload}
              isUploading={isUploading}
            />
          </div>


        </div>

        {/* Right Column - Info */}
        <div className="space-y-6">
          {/* Current Solution Info */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Solution Overview
            </h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Target className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="font-medium text-gray-900">Liability Decisions</div>
                  <div className="text-sm text-gray-600">AI-powered fault assessment</div>
                </div>
              </div>
              <div className="pt-4 border-t border-gray-200">
                <div className="text-sm text-gray-600 space-y-2">
                  <div>• Advanced document processing</div>
                  <div>• Evidence extraction & analysis</div>
                  <div>• Fault percentage calculation</div>
                  <div>• Confidence scoring</div>
                  <div>• Canadian legal compliance</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
