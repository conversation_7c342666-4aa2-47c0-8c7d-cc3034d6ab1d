// Configuration
const API_BASE_URL = 'http://localhost:8000/api/v1';

// DOM Elements
const uploadSection = document.getElementById('uploadSection');
const progressSection = document.getElementById('progressSection');
const resultsSection = document.getElementById('resultsSection');
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const faultAllocation = document.getElementById('faultAllocation');
const reasoningList = document.getElementById('reasoningList');

// State
let currentJobId = null;

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    
    // Check if we're on a results page (URL contains job ID)
    const urlPath = window.location.pathname;
    const jobIdMatch = urlPath.match(/\/results\/([a-f0-9-]+)/);
    if (jobIdMatch) {
        currentJobId = jobIdMatch[1];
        loadResults(currentJobId);
    }
});

function setupEventListeners() {
    // File input change
    fileInput.addEventListener('change', handleFileSelect);
    
    // Drag and drop
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', () => fileInput.click());
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = Array.from(e.dataTransfer.files);
    processFiles(files);
}

function handleFileSelect(e) {
    const files = Array.from(e.target.files);
    processFiles(files);
}

async function processFiles(files) {
    if (files.length === 0) return;
    
    // Show progress section
    showSection('progress');
    updateProgress(0, 'Preparing upload...');
    
    try {
        // Upload files
        updateProgress(20, 'Uploading files...');
        const jobId = await uploadFiles(files);
        currentJobId = jobId;
        
        // Update URL
        window.history.pushState({}, '', `/results/${jobId}`);
        
        // Start polling for results
        updateProgress(40, 'Processing documents...');
        await pollForResults(jobId);
        
    } catch (error) {
        console.error('Error processing files:', error);
        alert('Error processing files. Please try again.');
        showSection('upload');
    }
}

async function uploadFiles(files) {
    const formData = new FormData();
    
    files.forEach(file => {
        formData.append('files', file);
    });
    
    const response = await fetch(`${API_BASE_URL}/documents/upload`, {
        method: 'POST',
        body: formData
    });
    
    if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.job_id;
}

async function pollForResults(jobId) {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;
    
    while (attempts < maxAttempts) {
        try {
            const response = await fetch(`${API_BASE_URL}/documents/${jobId}/results`);
            
            if (!response.ok) {
                if (response.status === 404) {
                    // Still processing, continue polling
                    attempts++;
                    const progress = Math.min(40 + (attempts * 2), 90);
                    updateProgress(progress, 'Analyzing documents...');
                    await sleep(5000); // Wait 5 seconds
                    continue;
                }
                throw new Error(`API error: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.status === 'processing') {
                attempts++;
                const progress = Math.min(40 + (attempts * 2), 90);
                updateProgress(progress, 'Analyzing documents...');
                await sleep(5000);
                continue;
            }
            
            if (data.status === 'completed' && data.results) {
                updateProgress(100, 'Analysis complete!');
                await sleep(1000);
                displayResults(data.results);
                return;
            }
            
            // If we get here, something unexpected happened
            throw new Error('Unexpected response format');
            
        } catch (error) {
            console.error('Error polling for results:', error);
            attempts++;
            if (attempts >= maxAttempts) {
                throw error;
            }
            await sleep(5000);
        }
    }
    
    throw new Error('Timeout waiting for results');
}

async function loadResults(jobId) {
    showSection('progress');
    updateProgress(50, 'Loading results...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/documents/${jobId}/results`);
        
        if (!response.ok) {
            if (response.status === 404) {
                // Results not found, show mock data
                displayMockResults();
                return;
            }
            throw new Error(`API error: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.status === 'processing') {
            // Still processing, start polling
            await pollForResults(jobId);
        } else if (data.results) {
            updateProgress(100, 'Results loaded!');
            await sleep(500);
            displayResults(data.results);
        } else {
            displayMockResults();
        }
        
    } catch (error) {
        console.error('Error loading results:', error);
        displayMockResults();
    }
}

function displayResults(results) {
    // Extract liability decision data
    const liabilityDecision = results.liability_decision || {};
    const faultAllocationData = liabilityDecision.fault_allocation || {};
    const reasoning = liabilityDecision.reasoning || [];
    
    // Display fault allocation
    displayFaultAllocation(faultAllocationData);
    
    // Display reasoning
    displayReasoning(reasoning);
    
    showSection('results');
}

function displayMockResults() {
    const mockData = {
        fault_allocation: {
            "Party A": 75,
            "Party B": 25
        },
        reasoning: [
            "Traffic violation detected in Party A documentation",
            "Witness statements support Party B version of events", 
            "Physical evidence indicates Party A was speeding",
            "Party A failed to yield right of way"
        ]
    };
    
    displayFaultAllocation(mockData.fault_allocation);
    displayReasoning(mockData.reasoning);
    showSection('results');
}

function displayFaultAllocation(allocation) {
    faultAllocation.innerHTML = '';
    
    Object.entries(allocation).forEach(([party, percentage]) => {
        const partyDiv = document.createElement('div');
        partyDiv.className = 'party';
        partyDiv.innerHTML = `
            <div class="party-label">${party}</div>
            <div class="percentage">${percentage}%</div>
            <div class="percentage-bar">
                <div class="percentage-fill" style="width: ${percentage}%"></div>
            </div>
        `;
        faultAllocation.appendChild(partyDiv);
    });
}

function displayReasoning(reasoning) {
    reasoningList.innerHTML = '';
    
    reasoning.forEach(reason => {
        const li = document.createElement('li');
        li.textContent = reason;
        reasoningList.appendChild(li);
    });
}

function showSection(section) {
    uploadSection.classList.add('hidden');
    progressSection.classList.add('hidden');
    resultsSection.classList.add('hidden');
    
    switch(section) {
        case 'upload':
            uploadSection.classList.remove('hidden');
            break;
        case 'progress':
            progressSection.classList.remove('hidden');
            break;
        case 'results':
            resultsSection.classList.remove('hidden');
            break;
    }
}

function updateProgress(percentage, text) {
    progressFill.style.width = `${percentage}%`;
    progressText.textContent = text;
}

function resetApp() {
    currentJobId = null;
    fileInput.value = '';
    window.history.pushState({}, '', '/');
    showSection('upload');
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Handle browser back/forward buttons
window.addEventListener('popstate', function(e) {
    const urlPath = window.location.pathname;
    const jobIdMatch = urlPath.match(/\/results\/([a-f0-9-]+)/);
    
    if (jobIdMatch) {
        currentJobId = jobIdMatch[1];
        loadResults(currentJobId);
    } else {
        resetApp();
    }
});
